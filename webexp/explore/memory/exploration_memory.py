"""
Agent Memory System for Web Exploration

This module implements a comprehensive memory system to avoid redundant exploration
and improve exploration efficiency by tracking:
- Visited URLs and their exploration status
- Generated tasks and their outcomes
- Failed exploration attempts
- URL patterns and content types
"""

import json
import os
import hashlib
from dataclasses import dataclass, asdict
from typing import Dict, List, Set, Optional, Tuple
from collections import defaultdict
import logging

logger = logging.getLogger(__name__)

@dataclass
class URLMemory:
    """Memory entry for a specific URL"""
    url: str
    visit_count: int = 0
    last_visited: Optional[str] = None
    exploration_status: str = "unvisited"  # unvisited, exploring, completed, failed
    generated_tasks: List[str] = None
    successful_tasks: List[str] = None
    failed_tasks: List[str] = None
    content_type: Optional[str] = None
    page_features: Dict = None
    
    def __post_init__(self):
        if self.generated_tasks is None:
            self.generated_tasks = []
        if self.successful_tasks is None:
            self.successful_tasks = []
        if self.failed_tasks is None:
            self.failed_tasks = []
        if self.page_features is None:
            self.page_features = {}

@dataclass
class TaskMemory:
    """Memory entry for a specific task"""
    task_hash: str
    task_description: str
    attempted_urls: List[str] = None
    success_count: int = 0
    failure_count: int = 0
    last_attempt: Optional[str] = None
    success_patterns: List[str] = None
    failure_patterns: List[str] = None
    
    def __post_init__(self):
        if self.attempted_urls is None:
            self.attempted_urls = []
        if self.success_patterns is None:
            self.success_patterns = []
        if self.failure_patterns is None:
            self.failure_patterns = []

class ExplorationMemory:
    """
    Global memory system for web exploration to avoid redundant work
    """
    
    def __init__(self, memory_dir: str):
        self.memory_dir = memory_dir
        self.url_memory: Dict[str, URLMemory] = {}
        self.task_memory: Dict[str, TaskMemory] = {}
        self.url_patterns: Dict[str, List[str]] = defaultdict(list)
        self.exploration_stats = {
            "total_urls_discovered": 0,
            "total_urls_visited": 0,
            "total_tasks_generated": 0,
            "total_tasks_successful": 0,
            "redundant_visits_avoided": 0,
            "redundant_tasks_avoided": 0
        }
        
        os.makedirs(memory_dir, exist_ok=True)
        self.load_memory()
    
    def _task_hash(self, task_description: str) -> str:
        """Generate a hash for task deduplication"""
        # Normalize task description for better matching
        normalized = task_description.lower().strip()
        return hashlib.md5(normalized.encode()).hexdigest()
    
    def _categorize_url(self, url: str) -> str:
        """Categorize URL type for pattern analysis"""
        if "login" in url.lower():
            return "login"
        elif "register" in url.lower() or "signup" in url.lower():
            return "registration"
        elif "forum" in url.lower():
            return "forum"
        elif "wiki" in url.lower():
            return "wiki"
        elif "search" in url.lower():
            return "search"
        elif url.count('/') <= 3:
            return "main"
        else:
            return "content"
    
    def should_visit_url(self, url: str, max_visits: int = 3) -> Tuple[bool, str]:
        """
        Determine if a URL should be visited based on memory
        Returns (should_visit, reason)
        """
        if url not in self.url_memory:
            return True, "new_url"
        
        memory = self.url_memory[url]
        
        # Check visit count threshold
        if memory.visit_count >= max_visits:
            self.exploration_stats["redundant_visits_avoided"] += 1
            return False, f"max_visits_reached_{memory.visit_count}"
        
        # Check if exploration is completed
        if memory.exploration_status == "completed":
            return False, "exploration_completed"
        
        # Check if URL consistently fails
        if memory.exploration_status == "failed" and memory.visit_count >= 2:
            return False, "consistently_failing"
        
        return True, "should_explore"
    
    def should_generate_task(self, task_description: str, url: str) -> Tuple[bool, str]:
        """
        Determine if a task should be generated based on memory
        Returns (should_generate, reason)
        """
        task_hash = self._task_hash(task_description)
        
        # Check if task already exists globally
        if task_hash in self.task_memory:
            task_mem = self.task_memory[task_hash]
            
            # If task has been successful multiple times, avoid generating again
            if task_mem.success_count >= 2:
                self.exploration_stats["redundant_tasks_avoided"] += 1
                return False, f"task_already_successful_{task_mem.success_count}_times"
            
            # If task has been attempted on this URL before
            if url in task_mem.attempted_urls:
                if task_mem.failure_count > task_mem.success_count:
                    return False, "task_failed_on_this_url_before"
        
        # Check URL-specific task history
        if url in self.url_memory:
            url_mem = self.url_memory[url]
            if task_description in url_mem.failed_tasks:
                return False, "task_failed_on_url_before"
            if task_description in url_mem.successful_tasks:
                return False, "task_already_successful_on_url"
        
        return True, "should_generate"
    
    def record_url_visit(self, url: str, exploration_status: str = "exploring"):
        """Record a URL visit"""
        if url not in self.url_memory:
            self.url_memory[url] = URLMemory(url=url)
            self.exploration_stats["total_urls_discovered"] += 1
        
        self.url_memory[url].visit_count += 1
        self.url_memory[url].exploration_status = exploration_status
        
        if self.url_memory[url].visit_count == 1:
            self.exploration_stats["total_urls_visited"] += 1
        
        # Update URL patterns
        category = self._categorize_url(url)
        self.url_patterns[category].append(url)
    
    def record_task_generation(self, task_description: str, url: str):
        """Record task generation"""
        task_hash = self._task_hash(task_description)
        
        if task_hash not in self.task_memory:
            self.task_memory[task_hash] = TaskMemory(
                task_hash=task_hash,
                task_description=task_description
            )
            self.exploration_stats["total_tasks_generated"] += 1
        
        self.task_memory[task_hash].attempted_urls.append(url)
        
        # Update URL memory
        if url not in self.url_memory:
            self.url_memory[url] = URLMemory(url=url)
        self.url_memory[url].generated_tasks.append(task_description)
    
    def record_task_outcome(self, task_description: str, url: str, success: bool):
        """Record task execution outcome"""
        task_hash = self._task_hash(task_description)
        
        if task_hash in self.task_memory:
            if success:
                self.task_memory[task_hash].success_count += 1
                self.exploration_stats["total_tasks_successful"] += 1
            else:
                self.task_memory[task_hash].failure_count += 1
        
        # Update URL memory
        if url in self.url_memory:
            if success:
                if task_description not in self.url_memory[url].successful_tasks:
                    self.url_memory[url].successful_tasks.append(task_description)
            else:
                if task_description not in self.url_memory[url].failed_tasks:
                    self.url_memory[url].failed_tasks.append(task_description)
    
    def get_exploration_recommendations(self, current_url: str) -> Dict:
        """Get recommendations for exploration strategy"""
        recommendations = {
            "priority_urls": [],
            "avoid_urls": [],
            "suggested_tasks": [],
            "avoid_tasks": []
        }
        
        # Find URLs that should be prioritized (low visit count, high potential)
        for url, memory in self.url_memory.items():
            if memory.visit_count == 0:
                recommendations["priority_urls"].append(url)
            elif memory.visit_count >= 3 or memory.exploration_status == "completed":
                recommendations["avoid_urls"].append(url)
        
        # Find task patterns that work well
        successful_tasks = []
        for task_hash, task_mem in self.task_memory.items():
            if task_mem.success_count > task_mem.failure_count:
                successful_tasks.append(task_mem.task_description)
        
        recommendations["suggested_tasks"] = successful_tasks[:5]  # Top 5
        
        return recommendations
    
    def save_memory(self):
        """Save memory to disk"""
        memory_data = {
            "url_memory": {url: asdict(mem) for url, mem in self.url_memory.items()},
            "task_memory": {task_hash: asdict(mem) for task_hash, mem in self.task_memory.items()},
            "url_patterns": dict(self.url_patterns),
            "exploration_stats": self.exploration_stats
        }
        
        with open(os.path.join(self.memory_dir, "exploration_memory.json"), "w") as f:
            json.dump(memory_data, f, indent=2)
    
    def load_memory(self):
        """Load memory from disk"""
        memory_file = os.path.join(self.memory_dir, "exploration_memory.json")
        if os.path.exists(memory_file):
            try:
                with open(memory_file, "r") as f:
                    memory_data = json.load(f)
                
                # Load URL memory
                for url, mem_data in memory_data.get("url_memory", {}).items():
                    self.url_memory[url] = URLMemory(**mem_data)
                
                # Load task memory
                for task_hash, mem_data in memory_data.get("task_memory", {}).items():
                    self.task_memory[task_hash] = TaskMemory(**mem_data)
                
                # Load patterns and stats
                self.url_patterns = defaultdict(list, memory_data.get("url_patterns", {}))
                self.exploration_stats.update(memory_data.get("exploration_stats", {}))
                
                logger.info(f"Loaded exploration memory: {len(self.url_memory)} URLs, {len(self.task_memory)} tasks")
            except Exception as e:
                logger.warning(f"Failed to load exploration memory: {e}")
    
    def get_memory_stats(self) -> Dict:
        """Get current memory statistics"""
        return {
            **self.exploration_stats,
            "urls_in_memory": len(self.url_memory),
            "tasks_in_memory": len(self.task_memory),
            "url_categories": {cat: len(urls) for cat, urls in self.url_patterns.items()}
        }
